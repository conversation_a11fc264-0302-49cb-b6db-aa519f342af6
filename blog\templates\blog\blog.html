{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'blog/css/blog.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700;900&display=swap" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="tempus-gradus-blog">
    <!-- Masthead -->
    <div class="masthead text-center py-5">
        <h1 class="tempus-title">TEMPVS GRADVS</h1>
        <div class="masthead-divider">
            <span class="divider-line"></span>
            <span class="divider-icon"><i class="fas fa-hourglass"></i></span>
            <span class="divider-line"></span>
        </div>
        <p class="masthead-subtitle">CHRONICLES OF THE NEW ERA</p>
    </div>

    <div class="container">
        <!-- Featured Post Section (only on first page) -->
        {% if page_obj.number == 1 and posts %}
        <div class="featured-post-section mb-5">
            {% with featured_post=posts.0 %}
            <div class="row g-0">
                <div class="col-lg-8">
                    <div class="featured-image-container">
                        {% if featured_post.post_featured_image %}
                            <img src="{{ featured_post.post_featured_image.url }}" 
                                 alt="{{ featured_post.post_title }}" 
                                 class="img-fluid">
                        {% else %}
                            <img src="{% static 'blog/images/default-blog-image.png' %}" 
                                 alt="Default blog image" 
                                 class="img-fluid">
                        {% endif %}
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="featured-content p-4">
                        <div class="featured-badge mb-2">FEATURED</div>
                        <h2 class="featured-title">
                            <a href="{% url 'post_detail' featured_post.post_slug %}" class="text-decoration-none">
                                {{ featured_post.post_title }}
                            </a>
                        </h2>
                        <div class="featured-meta mb-3">
                            <span class="me-3">
                                <i class="fas fa-user-edit me-1"></i>
                                {{ featured_post.post_author.get_full_name|default:featured_post.post_author.username }}
                            </span>
                            <span>
                                <i class="far fa-calendar-alt me-1"></i>
                                {{ featured_post.post_created_on|date:"F j, Y" }}
                            </span>
                        </div>
                        <p class="featured-excerpt">
                            {{ featured_post.post_excerpt|striptags|safe }}
                        </p>
                        <a href="{% url 'post_detail' featured_post.post_slug %}" 
                           class="btn btn-primary"
                           aria-label="Read more about {{ featured_post.post_title }}">
                            Read Full Article
                            <i class="fas fa-arrow-right ms-1" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% endwith %}
        </div>
        {% endif %}

        <div class="row">
            <!-- Main content -->
            <div class="col-lg-9 order-lg-1 order-2">
                <!-- Active filters -->
                {% if current_category or current_tag %}
                <div class="active-filters mb-4">
                    <h5 class="filter-heading">Active Filters:</h5>
                    {% if current_category %}
                    <a href="{% url 'blog' %}" class="badge bg-primary text-decoration-none me-2">
                        Category: {{ current_category }} <i class="fas fa-times"></i>
                    </a>
                    {% endif %}
                    {% if current_tag %}
                    <a href="{% url 'blog' %}" class="badge bg-secondary text-decoration-none">
                        Tag: {{ current_tag }} <i class="fas fa-times"></i>
                    </a>
                    {% endif %}
                </div>
                {% endif %}

                {% if posts %}
                    <!-- Skip the first post if it's the first page (already shown as featured) -->
                    {% if page_obj.number == 1 %}
                        {% with posts_to_display=posts|slice:"1:" %}
                            <div class="row g-4">
                                {% for post in posts_to_display %}
                                    <div class="col-md-6 col-lg-4">
                                        <article class="card blog-card h-100">
                                            <div class="blog-image-container">
                                                {% if post.post_featured_image %}
                                                    <img src="{{ post.post_featured_image.url }}" 
                                                         alt="{{ post.post_title }}" 
                                                         class="card-img-top img-fluid">
                                                {% else %}
                                                    <img src="{% static 'blog/images/default-blog-image.png' %}" 
                                                         alt="Default blog image" 
                                                         class="card-img-top img-fluid">
                                                {% endif %}
                                            </div>
                                            
                                            <div class="card-body">
                                                <h2 class="card-title h5 mb-3">
                                                    <a href="{% url 'post_detail' post.post_slug %}" 
                                                       class="text-decoration-none"
                                                       aria-label="Read full post: {{ post.post_title }}">
                                                        {{ post.post_title }}
                                                    </a>
                                                </h2>
                                                
                                                <div class="blog-meta mb-2">
                                                    <span class="me-3">
                                                        <i class="fas fa-user-edit me-1"></i>
                                                        {{ post.post_author.get_full_name|default:post.post_author.username }}
                                                    </span>
                                                    <span>
                                                        <i class="far fa-calendar-alt me-1"></i>
                                                        {{ post.post_created_on|date:"F j, Y" }}
                                                    </span>
                                                </div>

                                                {% if post.category %}
                                                <div class="mb-2">
                                                    <a href="{% url 'blog' %}?category={{ post.category.slug }}" class="badge bg-primary text-decoration-none">
                                                        <i class="fas fa-folder"></i> {{ post.category.name }}
                                                    </a>
                                                </div>
                                                {% endif %}
                                                
                                                <p class="card-text blog-excerpt">
                                                    {{ post.post_excerpt|striptags|safe }}
                                                </p>

                                                <div class="tags mb-3">
                                                    {% for tag in post.tags.all %}
                                                    <a href="{% url 'blog' %}?tag={{ tag.slug }}" class="badge bg-secondary text-decoration-none me-1">
                                                        <i class="fas fa-tag"></i> {{ tag.name }}
                                                    </a>
                                                    {% endfor %}
                                                </div>
                                                
                                                <a href="{% url 'post_detail' post.post_slug %}" 
                                                   class="btn btn-outline-primary btn-sm"
                                                   aria-label="Read more about {{ post.post_title }}">
                                                    Read More
                                                    <i class="fas fa-arrow-right ms-1" aria-hidden="true"></i>
                                                </a>
                                            </div>
                                        </article>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endwith %}
                    {% else %}
                        <div class="row g-4">
                            {% for post in posts %}
                                <div class="col-md-6 col-lg-4">
                                    <article class="card blog-card h-100">
                                        <div class="blog-image-container">
                                            {% if post.post_featured_image %}
                                                <img src="{{ post.post_featured_image.url }}" 
                                                     alt="{{ post.post_title }}" 
                                                     class="card-img-top img-fluid">
                                            {% else %}
                                                <img src="{% static 'blog/images/default-blog-image.png' %}" 
                                                     alt="Default blog image" 
                                                     class="card-img-top img-fluid">
                                            {% endif %}
                                        </div>
                                        
                                        <div class="card-body">
                                            <h2 class="card-title h5 mb-3">
                                                <a href="{% url 'post_detail' post.post_slug %}" 
                                                   class="text-decoration-none"
                                                   aria-label="Read full post: {{ post.post_title }}">
                                                    {{ post.post_title }}
                                                </a>
                                            </h2>
                                            
                                            <div class="blog-meta mb-2">
                                                <span class="me-3">
                                                    <i class="fas fa-user-edit me-1"></i>
                                                    {{ post.post_author.get_full_name|default:post.post_author.username }}
                                                </span>
                                                <span>
                                                    <i class="far fa-calendar-alt me-1"></i>
                                                    {{ post.post_created_on|date:"F j, Y" }}
                                                </span>
                                            </div>

                                            {% if post.category %}
                                            <div class="mb-2">
                                                <a href="{% url 'blog' %}?category={{ post.category.slug }}" class="badge bg-primary text-decoration-none">
                                                    <i class="fas fa-folder"></i> {{ post.category.name }}
                                                </a>
                                            </div>
                                            {% endif %}
                                            
                                            <p class="card-text blog-excerpt">
                                                {{ post.post_excerpt|striptags|safe }}
                                            </p>

                                            <div class="tags mb-3">
                                                {% for tag in post.tags.all %}
                                                <a href="{% url 'blog' %}?tag={{ tag.slug }}" class="badge bg-secondary text-decoration-none me-1">
                                                    <i class="fas fa-tag"></i> {{ tag.name }}
                                                </a>
                                                {% endfor %}
                                            </div>
                                            
                                            <a href="{% url 'post_detail' post.post_slug %}" 
                                               class="btn btn-outline-primary btn-sm"
                                               aria-label="Read more about {{ post.post_title }}">
                                                Read More
                                                <i class="fas fa-arrow-right ms-1" aria-hidden="true"></i>
                                            </a>
                                        </div>
                                    </article>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    {% if is_paginated %}
                        <nav aria-label="Blog pagination" class="mt-5">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}"
                                           aria-label="Go to previous page">
                                            <i class="fas fa-chevron-left" aria-hidden="true"></i>
                                            Previous
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}"
                                               aria-label="Go to page {{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}"
                                           aria-label="Go to next page">
                                            Next
                                            <i class="fas fa-chevron-right" aria-hidden="true"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <h2 class="h4">No blog posts available yet.</h2>
                        <p>Check back soon for new content!</p>
                    </div>
                {% endif %}
            </div>

            <!-- Sidebar with filters -->
            <div class="col-lg-3 order-lg-2 order-1 mb-4 mb-lg-0">
                <div class="sidebar sticky-top">
                    <!-- Categories -->
                    <div class="sidebar-section categories mb-4">
                        <h4 class="sidebar-heading">Categories</h4>
                        <div class="category-list">
                            <a href="{% url 'blog' %}" class="category-item {% if not current_category %}active{% endif %}">
                                All Categories
                            </a>
                            {% for category in categories %}
                            <a href="{% url 'blog' %}?category={{ category.slug }}" 
                               class="category-item d-flex justify-content-between align-items-center {% if current_category == category.slug %}active{% endif %}">
                                {{ category.name }}
                                <span class="category-count">{{ category.post_count }}</span>
                            </a>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Popular Tags -->
                    <div class="sidebar-section popular-tags">
                        <h4 class="sidebar-heading">Popular Tags</h4>
                        <div class="tags-cloud">
                            {% for tag in popular_tags %}
                            <a href="{% url 'blog' %}?tag={{ tag.slug }}" 
                               class="tag-badge {% if current_tag == tag.slug %}active{% endif %}">
                                {{ tag.name }} <span class="tag-count">{{ tag.num_times }}</span>
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
