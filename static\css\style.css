body {
    font-family: "Libre Caslon Text", serif;
    padding-top: 72px;
    background: url('/media/cover-logo.jpg') no-repeat center center fixed;
    background-size: cover;
    height: calc(100vh - 164px);
}

.social-links a {
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #882222 !important;
}

.block {
    color: white;
}

/* Allauth form formating */
.allauth-form-inner-content p {
    margin-top: 1.5rem;
}

.allauth-form-inner-content input {
    color: #ffffff;
    border-color: #ffffff;
}

.allauth-form-inner-content input::placeholder {
    color: #807c7c;
}
.allauth-form-inner-content label:not [for="id_remember"]{
    display: none;
}

/* Back to Top Button */
#back-to-top {
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    background-color: #882222;
    transition-property: transform, background-color;
    transition-duration: 0.3s;
    transition-timing-function: ease-in-out;
}

#back-to-top:hover {
    background-color: #aa3333;
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(136, 34, 34, 0.4);
}

/* signup-form */
input, textarea {
    color: black !important;
}

/* Navigation Profile Image */
.nav-profile-image {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #fff;
}

.nav-profile-image:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.nav-link.dropdown-toggle {
    padding: 0.5rem 1rem;
}

/* Update and Remove Links */
.update-link,
.remove-item {
    cursor: pointer;
    transition: color 0.3s ease;
    border: 0;
    background: transparent;
    padding: 0;
}

.update-link:hover {
    color: #0dcaf0 !important;
}

.remove-item:hover {
    color: #dc3545 !important;
}

/* Text muted styling */
.text-muted {
    color: #6c757d !important;
    font-style: italic;
}

/* Text color */
.text-white {
    color: #fff !important;
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1019;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none;
}

.mobile-menu-overlay.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Ensure the navbar has a higher z-index than the overlay */
header .navbar {
    position: relative;
    z-index: 1030;
}
