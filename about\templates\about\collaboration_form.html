{% load widget_tweaks %}

{% if form.non_field_errors %}
<div class="alert alert-danger">
    {% for error in form.non_field_errors %}
        {{ error }}
    {% endfor %}
</div>
{% endif %}

{# First render the basic fields #}
{% for field in form %}
    {% if field.name != 'rating' and field.name != 'book_id' %}
    <div class="form-group mb-3">
        <label for="{{ field.id_for_label }}" class="form-label">
            {{ field.label }}
            {% if field.field.required %}*{% endif %}
        </label>
        {{ field }}
        {% if field.errors %}
        <div class="invalid-feedback d-block">
            {{ field.errors|join:", " }}
        </div>
        {% endif %}
    </div>
    {% endif %}
{% endfor %}

{# Then render the book review specific fields #}
<div class="form-group mb-3 book-review-field" style="display: none;">
    <label for="id_book_id" class="form-label">Select Book</label>
    {{ form.book_id }}
    {% if form.book_id.errors %}
    <div class="invalid-feedback d-block">
        {{ form.book_id.errors|join:", " }}
    </div>
    {% endif %}
</div>

<div class="form-group mb-3 book-review-field" style="display: none;">
    <label class="form-label">Book Rating</label>
    <div class="stars-container">
        {% for i in "12345"|make_list %}
        <input type="radio" id="star{{ i }}" name="rating" value="{{ i }}" {% if form.rating.value == i %}checked{% endif %} class="rating-input">
        <label for="star{{ i }}" title="{{ i }} stars" class="star-rating" data-value="{{ i }}">★</label>
        {% endfor %}
        <span class="rating-value ms-2">{% if form.rating.value %}{{ form.rating.value }}{% else %}0{% endif %}</span>
    </div>
    {% if form.rating.errors %}
    <div class="invalid-feedback d-block">
        {{ form.rating.errors|join:", " }}
    </div>
    {% endif %}
</div>

<button type="submit" class="btn btn-primary">Submit</button>
