document.addEventListener('DOMContentLoaded', function() {
    // Get references to navbar elements
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    // Function to close the navbar when clicking outside
    document.addEventListener('click', function(event) {
        // Only proceed if the navbar is expanded
        const isNavbarExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
        
        // Check if click was outside the navbar and the navbar is expanded
        if (isNavbarExpanded && 
            !navbarCollapse.contains(event.target) && 
            !navbarToggler.contains(event.target)) {
            
            // Simulate a click on the navbar toggler to close it
            navbarToggler.click();
        }
    });
    
    // Function to close navbar on scroll
    let lastScrollTop = 0;
    window.addEventListener('scroll', function() {
        // Only proceed if navbar is expanded
        const isNavbarExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
        
        if (isNavbarExpanded) {
            // Get current scroll position
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Check if user has scrolled more than 10px
            if (Math.abs(lastScrollTop - scrollTop) > 10) {
                // Close the navbar
                navbarToggler.click();
            }
            
            lastScrollTop = scrollTop;
        }
    }, { passive: true }); // Using passive listener for better scroll performance
});