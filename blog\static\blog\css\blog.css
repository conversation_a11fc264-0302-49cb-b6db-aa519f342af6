/* Tempus Gradus Blog - Futuristic Magazine Styling */

/* Main Blog Container */
.tempus-gradus-blog {
    background-color: #000;
    color: #fff;
    font-family: 'Libre Caslon Text', serif;
}

/* Masthead Styling */
.masthead {
    background-color: rgba(0, 0, 0, 0.8);
    padding: 3rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tempus-title {
    font-family: 'Cin<PERSON>', serif;
    font-size: 3.5rem;
    font-weight: 900;
    letter-spacing: 0.5rem;
    margin-bottom: 0.5rem;
    color: #fff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.masthead-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem auto;
    width: 50%;
}

.divider-line {
    flex-grow: 1;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.divider-icon {
    margin: 0 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.masthead-subtitle {
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    letter-spacing: 0.2rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Featured Post Section */
.featured-post-section {
    background-color: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
    overflow: hidden;
    margin-bottom: 3rem;
}

.featured-image-container {
    height: 400px;
    overflow: hidden;
    position: relative;
}

.featured-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-content {
    background-color: rgba(0, 0, 0, 0.8);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.featured-badge {
    display: inline-block;
    background-color: #a9b32b;
    color: #000;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: bold;
    letter-spacing: 0.1rem;
    border-radius: 0.25rem;
}

.featured-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.featured-title a {
    color: #fff;
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

.featured-title a:hover {
    color: #a9b32b;
}

.featured-meta {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.featured-excerpt {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

/* Blog Cards */
.card.blog-card {
    background-color: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.card.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.blog-image-container {
    height: 200px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000;
}

.blog-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-body {
    padding: 1.5rem;
    color: #fff;
}

.card-title a {
    color: #fff;
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

.card-title a:hover {
    color: #a9b32b;
}

.blog-meta {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1rem;
}

.blog-excerpt {
    color: rgba(255, 255, 255, 0.9);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 1rem;
}

/* Sidebar Styling */
.sidebar {
    background-color: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    position: sticky;
    top: 2rem;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-heading {
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    color: #fff;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Category List */
.category-list {
    display: flex;
    flex-direction: column;
}

.category-item {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease-in-out;
}

.category-item:hover, .category-item.active {
    color: #a9b32b;
    background-color: rgba(169, 179, 43, 0.1);
    padding-left: 0.5rem;
}

.category-count {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

/* Tags Cloud */
.tags-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-badge {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 0.3rem 0.6rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    transition: all 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
}

.tag-badge:hover, .tag-badge.active {
    background-color: rgba(169, 179, 43, 0.3);
    color: #fff;
}

.tag-count {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    margin-left: 0.3rem;
}

/* Filter Styling */
.filter-heading {
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    color: #fff;
    margin-bottom: 0.5rem;
}

.active-filters .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* Pagination */
.pagination {
    margin-top: 3rem;
}

.pagination .page-link {
    background-color: rgba(0, 0, 0, 0.7);
    border-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.pagination .page-link:hover {
    background-color: rgba(169, 179, 43, 0.2);
    border-color: rgba(169, 179, 43, 0.3);
    color: #fff;
}

.pagination .page-item.active .page-link {
    background-color: rgba(169, 179, 43, 0.7);
    border-color: rgba(169, 179, 43, 0.8);
    color: #fff;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .tempus-title {
        font-size: 2.5rem;
    }
    
    .featured-image-container {
        height: 300px;
    }
    
    .sidebar {
        position: static;
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .tempus-title {
        font-size: 2rem;
        letter-spacing: 0.3rem;
    }
    
    .masthead-divider {
        width: 80%;
    }
    
    .featured-image-container {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .tempus-title {
        font-size: 1.8rem;
        letter-spacing: 0.2rem;
    }
    
    .masthead-subtitle {
        font-size: 1rem;
    }
    
    .featured-title {
        font-size: 1.5rem;
    }
}
