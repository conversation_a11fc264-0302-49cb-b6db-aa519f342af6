from django.http import HttpResponseForbidden
from django.views.generic import ListView, DetailView
from django.shortcuts import get_object_or_404, redirect, render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count
from .models import Post, Comment, Category
from .forms import CommentForm
from utils.rate_limit import rate_limit
from django.utils.decorators import method_decorator


class BlogListView(ListView):
    """
    View for displaying a paginated list of published blog posts.
    """
    model = Post
    template_name = 'blog/blog.html'
    context_object_name = 'posts'
    paginate_by = 6

    def get_queryset(self):
        """
        Returns queryset of published posts filtered
        by tag or category if specified.
        """
        queryset = Post.objects.filter(post_status=1)

        # Filter by tag if specified
        tag_slug = self.request.GET.get('tag')
        if tag_slug:
            queryset = queryset.filter(tags__slug=tag_slug)

        # Filter by category if specified
        category_slug = self.request.GET.get('category')
        if category_slug:
            queryset = queryset.filter(category__slug=category_slug)

        return queryset.order_by('-post_created_on')

    def get_context_data(self, **kwargs):
        """
        Adds title, categories, and popular tags to the context data.
        """
        context = super().get_context_data(**kwargs)
        context['title'] = 'Blog'

        # Add all categories
        context['categories'] = Category.objects.annotate(
            post_count=Count('posts')
        ).order_by('name')

        # Add popular tags (top 10)
        context['popular_tags'] = Post.tags.most_common()[:10]

        # Add current filters
        context['current_tag'] = self.request.GET.get('tag')
        context['current_category'] = self.request.GET.get('category')

        return context


@method_decorator(rate_limit('post_detail', limit=30, period=60), name='dispatch')
class PostDetailView(DetailView):
    """
    View for displaying a single blog post with its comments.
    """
    model = Post
    template_name = 'blog/post_detail.html'
    context_object_name = 'post'
    slug_url_kwarg = 'slug'
    slug_field = 'post_slug'

    def get_queryset(self):
        """
        Returns queryset of published posts only.
        """
        return Post.objects.filter(post_status=1)

    def get_context_data(self, **kwargs):
        """
        Adds comments, comment form, and related posts to the context data.
        """
        context = super().get_context_data(**kwargs)
        post = self.object

        # Add comments
        context['comments'] = post.comments.filter(active=True)
        if self.request.user.is_authenticated:
            context['form'] = CommentForm()

        # Get related posts based on tags and category
        related_posts = Post.objects.filter(
            post_status=1  # Only published posts
        ).exclude(
            id=post.id
        )

        # First try to get posts with same tags
        if post.tags.exists():
            related_posts = related_posts.filter(
                tags__in=post.tags.all()
            ).distinct()

        # If not enough related posts by tags, add posts from same category
        if related_posts.count() < 6 and post.category:
            category_posts = Post.objects.filter(
                post_status=1,
                category=post.category
            ).exclude(
                id=post.id
            ).exclude(
                id__in=related_posts.values_list('id', flat=True)
            )
            related_posts = list(related_posts) + list(category_posts)

        # Limit to 6 posts
        context['related_posts'] = related_posts[:6]

        return context

    def post(self, request, *args, **kwargs):
        """
        Handle POST requests for submitting comments on a post.
        """
        post = self.get_object()
        form = CommentForm(request.POST)
        
        if not request.user.is_authenticated:
            messages.error(
                request, 
                "You must be logged in to comment."
            )
            return redirect(post.get_absolute_url())
            
        if form.is_valid():
            comment = form.save(commit=False)
            comment.post = post
            comment.author = request.user
            comment.save()
            
            messages.success(
                request, 
                "Your comment has been added successfully!"
            )
            return redirect(post.get_absolute_url())
        
        # If form is invalid, re-render the page with the form
        context = self.get_context_data(object=post)
        context['form'] = form
        return self.render_to_response(context)


@login_required
@rate_limit('comment_edit', limit=5, period=300)
def comment_edit(request, post_slug, comment_id):
    """
    View for editing an existing comment.
    """
    comment = get_object_or_404(
        Comment, id=comment_id, post__post_slug=post_slug)

    if not request.user == comment.author:
        return HttpResponseForbidden(
            "You don't have permission to edit this comment."
        )

    if request.method == 'POST':
        form = CommentForm(request.POST, instance=comment)
        if form.is_valid():
            form.save()
            messages.success(request, 'Comment updated successfully.')
            return redirect('post_detail', slug=post_slug)
    else:
        form = CommentForm(instance=comment)

    return render(request, 'blog/comment_edit.html', {
        'form': form,
        'comment': comment,
        'post': comment.post
    })


@login_required
@rate_limit('comment_delete', limit=5, period=300)
def comment_delete(request, post_slug, comment_id):
    """
    View for deleting an existing comment.
    """
    comment = get_object_or_404(
        Comment, id=comment_id, post__post_slug=post_slug)

    if not (request.user == comment.author or request.user.is_staff):
        return HttpResponseForbidden(
            "You don't have permission to delete this comment."
        )

    if request.method == 'POST':
        comment.delete()
        messages.success(request, 'Comment deleted successfully.')

    return redirect('post_detail', slug=post_slug)
