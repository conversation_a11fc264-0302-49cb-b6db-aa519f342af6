{% extends "contest/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Simplified star rating styles */
    .rating-container {
        display: inline-flex;
        flex-direction: row;
        font-size: 2.5rem; /* Even larger stars */
        padding: 15px 0;
    }

    .rating-container i {
        cursor: pointer;
        margin-right: 15px;
        color: #ccc; /* Unselected star color */
        transition: all 0.2s ease;
        padding: 8px; /* Larger clickable area */
        border-radius: 50%; /* Make the clickable area circular */
        background-color: transparent; /* Transparent background */
    }

    .rating-container i:hover {
        background-color: rgba(0, 0, 0, 0.05); /* Light background on hover */
    }

    .rating-container i.fas {
        color: #ffc107; /* Gold color for selected stars */
    }

    .rating-container i.clicked {
        transform: scale(1.3); /* Enlarge when clicked */
        color: #ffc107 !important; /* Ensure gold color */
    }

    /* Make the star rating more prominent */
    .star-rating {
        padding: 10px 0;
        margin-bottom: 15px;
        border-radius: 5px;
        background-color: rgba(0, 0, 0, 0.02); /* Very light background */
        padding: 15px;
    }

    /* Add a message to indicate the stars are clickable */
    .rating-status {
        margin-top: 10px;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block contest_title %}
    {% if submission %}
        Edit Submission
    {% else %}
        Submit Your Entry
    {% endif %}
{% endblock %}

{% block contest_content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title mb-0">
                    {% if submission %}
                        Edit Your Submission
                    {% else %}
                        Submit Your Entry for {{ contest.name }}
                    {% endif %}
                </h2>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="submission-form">
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <h3>Submission Details</h3>
                            {{ form.title|as_crispy_field }}
                            {{ form.category|as_crispy_field }}
                            {{ form.description|as_crispy_field }}

                            <div class="mb-3">
                                <label for="{{ form.file.id_for_label }}" class="form-label">Artwork File</label>
                                {{ form.file }}
                                <div class="form-text">
                                    <ul>
                                        <li>Digital Art/Graphics: Submit as PNG or JPG, minimum 2,000 px width, max 10 MB.</li>
                                        <li>Traditional Painting/Drawing/Sketch: Photograph with even lighting, camera centered, no skew.</li>
                                        <li>3D Models: Submit a PNG render/screenshot of your model. Then provide a link to your 3D model in the field below.</li>
                                    </ul>
                                </div>
                                {% if form.file.errors %}
                                    <div class="alert alert-danger">
                                        {{ form.file.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- 3D Model URL field - will be shown/hidden based on category -->
                            <div id="model-3d-url-container" class="mb-3 {% if not '3D' in form.instance.category.name|default:'' %}d-none{% endif %}">
                                <label for="{{ form.model_3d_url.id_for_label }}" class="form-label">3D Model URL</label>
                                {{ form.model_3d_url }}
                                <div class="form-text">
                                    <p>Provide a link to your 3D model on a platform like:</p>
                                    <ul>
                                        <li><a href="https://sketchfab.com/" target="_blank">Sketchfab</a> (recommended) - You can use either:</li>
                                        <ul>
                                            <li>The regular URL from your browser (e.g., <code>https://sketchfab.com/3d-models/angel-wings-a575b36467d840be84d8f91d5074d7d4</code>)</li>
                                            <li>OR the embed URL from the Share button (e.g., <code>https://sketchfab.com/models/a575b36467d840be84d8f91d5074d7d4/embed</code>)</li>
                                        </ul>
                                        <li><a href="https://www.artstation.com/" target="_blank">ArtStation</a></li>
                                        <li><a href="https://itch.io/" target="_blank">itch.io</a></li>
                                        <li>Or any other platform that allows 3D model viewing</li>
                                    </ul>
                                    <p class="mt-2"><strong>Note:</strong> For Sketchfab models, the system will automatically embed the 3D viewer in the submission page. For other platforms, a link will be provided.</p>
                                    <div class="alert alert-info mt-2">
                                        <p class="mb-0"><strong>Important:</strong> Do NOT use shortened URLs (like <code>https://skfb.ly/pu7Pw</code>) as they won't work with our embedding system.</p>
                                    </div>
                                </div>
                                {% if form.model_3d_url.errors %}
                                    <div class="alert alert-danger">
                                        {{ form.model_3d_url.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                {{ form.ai_tools_used|as_crispy_field }}
                            </div>

                            <div id="ai-tools-details" class="mb-3 {% if not form.ai_tools_used.value %}d-none{% endif %}">
                                {{ form.ai_tools_details|as_crispy_field }}
                            </div>

                            {% if submission and submission.file %}
                                <div class="mb-3">
                                    <p>Current file: <a href="{{ submission.file.url }}" target="_blank">View</a></p>
                                    {% if submission.file.url|lower|slice:"-4:" == '.jpg' or submission.file.url|lower|slice:"-5:" == '.jpeg' or submission.file.url|lower|slice:"-4:" == '.png' %}
                                        <img src="{{ submission.file.url }}" alt="{{ submission.title }}" class="img-thumbnail" style="max-height: 200px;">
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <h3>Book Reference</h3>
                            <div class="alert alert-info">
                                <p>Your submission must be inspired by a part of The Age of New Era book series. Please provide the following details:</p>
                            </div>

                            {{ form.book_part|as_crispy_field }}

                            <div class="form-group mb-3">
                                <label for="id_chapter_reference">Chapter Reference</label>
                                <select id="id_chapter_reference" name="chapter_reference" class="form-control form-select">
                                    {% if form.instance.chapter_reference %}
                                        {% if "Part" in form.instance.chapter_reference %}
                                            {% with chapter_ref=form.instance.chapter_reference|cut:"Part 1: The Origin - "|cut:"Part 2: The Scrutiny - "|cut:"Part 3: The Tempus - " %}
                                                <option value="{{ chapter_ref }}" selected>{{ chapter_ref }}</option>
                                            {% endwith %}
                                        {% else %}
                                            <option value="{{ form.instance.chapter_reference }}" selected>{{ form.instance.chapter_reference }}</option>
                                        {% endif %}
                                    {% else %}
                                        <option value="">Select a chapter</option>
                                    {% endif %}
                                </select>
                                <div class="form-text">Select the chapter that inspired your artwork.</div>

                                <!-- Hidden template for chapter references -->
                                <div id="chapter-reference-template" style="display: none;">
                                    <!-- Part 1: The Origin -->
                                    <optgroup label="Part 1: The Origin" data-part="part1">
                                        <option value="Chapter 1: Straight Street Ground">Chapter 1: Straight Street Ground</option>
                                        <option value="Chapter 2: Deep Loopia">Chapter 2: Deep Loopia</option>
                                        <option value="Chapter 3: Tigers and Cages">Chapter 3: Tigers and Cages</option>
                                        <option value="Chapter 4: Sunday Shade">Chapter 4: Sunday Shade</option>
                                        <option value="Chapter 5: Impact Choice">Chapter 5: Impact Choice</option>
                                        <option value="Chapter 6: Conscientious Consequence">Chapter 6: Conscientious Consequence</option>
                                        <option value="Chapter 7: Enlightening Storm">Chapter 7: Enlightening Storm</option>
                                        <option value="Chapter 8: Journey">Chapter 8: Journey</option>
                                        <option value="Chapter 9: Rerouted Trip">Chapter 9: Rerouted Trip</option>
                                        <option value="Chapter 10: Dragon Dance">Chapter 10: Dragon Dance</option>
                                        <option value="Chapter 11: Simpler Times">Chapter 11: Simpler Times</option>
                                        <option value="Chapter 12: The Enlivening Happening">Chapter 12: The Enlivening Happening</option>
                                        <option value="Chapter 13: The Seal">Chapter 13: The Seal</option>
                                        <option value="Chapter 14: Change">Chapter 14: Change</option>
                                        <option value="Chapter 15: The Spirit Drive">Chapter 15: The Spirit Drive</option>
                                        <option value="Chapter 16: Initial Intention">Chapter 16: Initial Intention</option>
                                        <option value="Chapter 17: Big Escape">Chapter 17: Big Escape</option>
                                    </optgroup>
                                    <!-- Part 2: The Scrutiny -->
                                    <optgroup label="Part 2: The Scrutiny" data-part="part2">
                                        <option value="Chapter 18: Questioning Knowledge">Chapter 18: Questioning Knowledge</option>
                                        <option value="Chapter 19: Contemplation Blizzard">Chapter 19: Contemplation Blizzard</option>
                                        <option value="Chapter 20: Baby Steps">Chapter 20: Baby Steps</option>
                                        <option value="Chapter 21: Veracity">Chapter 21: Veracity</option>
                                        <option value="Chapter 22: Information Dumping">Chapter 22: Information Dumping</option>
                                        <option value="Chapter 23: Conscious Insights">Chapter 23: Conscious Insights</option>
                                        <option value="Chapter 24: Misleading Myths">Chapter 24: Misleading Myths</option>
                                        <option value="Chapter 25: Streams of Consciousness">Chapter 25: Streams of Consciousness</option>
                                        <option value="Chapter 26: Humanity Composition">Chapter 26: Humanity Composition</option>
                                        <option value="Chapter 27: Universal Wisdom">Chapter 27: Universal Wisdom</option>
                                    </optgroup>
                                    <!-- Part 3: The Tempus -->
                                    <optgroup label="Part 3: The Tempus" data-part="part3">
                                        <option value="Chapter 28: The Reverse Day">Chapter 28: The Reverse Day</option>
                                        <option value="Chapter 29: Vision Layers">Chapter 29: Vision Layers</option>
                                        <option value="Chapter 30: Test Flight">Chapter 30: Test Flight</option>
                                        <option value="Chapter 31: The G">Chapter 31: The G</option>
                                        <option value="Chapter 32: Whale's Awakening">Chapter 32: Whale's Awakening</option>
                                        <option value="Chapter 33: Core Issues">Chapter 33: Core Issues</option>
                                        <option value="Chapter 34: Early Preparations">Chapter 34: Early Preparations</option>
                                        <option value="Chapter 35: Responsibility Whip">Chapter 35: Responsibility Whip</option>
                                        <option value="Chapter 36: Red Destination">Chapter 36: Red Destination</option>
                                        <option value="Chapter 37: Modification Exploration">Chapter 37: Modification Exploration</option>
                                        <option value="Chapter 38: The Message">Chapter 38: The Message</option>
                                        <option value="Chapter 39: Kind Rebellion">Chapter 39: Kind Rebellion</option>
                                        <option value="Chapter 40: Enigmatic Feedback">Chapter 40: Enigmatic Feedback</option>
                                        <option value="Chapter 41: Arduous Task">Chapter 41: Arduous Task</option>
                                        <option value="Chapter 42: The TUP">Chapter 42: The TUP</option>
                                        <option value="Chapter 43: New Systems">Chapter 43: New Systems</option>
                                        <option value="Chapter 44: Camping Time">Chapter 44: Camping Time</option>
                                        <option value="Chapter 45: Thoughtful Advancement">Chapter 45: Thoughtful Advancement</option>
                                        <option value="Chapter 46: Balanced Progress">Chapter 46: Balanced Progress</option>
                                        <option value="Chapter 47: The Expedition">Chapter 47: The Expedition</option>
                                        <option value="Chapter 48: Edgy Middle">Chapter 48: Edgy Middle</option>
                                        <option value="Chapter 49: Dashing Tempus">Chapter 49: Dashing Tempus</option>
                                    </optgroup>
                                </div>
                            </div>

                            <div class="review-fields-container">
                                <div class="form-group mb-3">
                                    <label for="{{ form.rating.id_for_label }}">{{ form.rating.label }}</label>
                                    <div class="star-rating">
                                        <input type="hidden" name="rating" id="id_rating" value="{{ form.instance.rating|default:'0' }}">
                                        <div class="rating-container" role="group" aria-label="Rating stars">
                                            <i class="far fa-star" data-rating="1" role="button" tabindex="0" aria-label="Rate 1 star" title="Rate 1 star"></i>
                                            <i class="far fa-star" data-rating="2" role="button" tabindex="0" aria-label="Rate 2 stars" title="Rate 2 stars"></i>
                                            <i class="far fa-star" data-rating="3" role="button" tabindex="0" aria-label="Rate 3 stars" title="Rate 3 stars"></i>
                                            <i class="far fa-star" data-rating="4" role="button" tabindex="0" aria-label="Rate 4 stars" title="Rate 4 stars"></i>
                                            <i class="far fa-star" data-rating="5" role="button" tabindex="0" aria-label="Rate 5 stars" title="Rate 5 stars"></i>
                                        </div>
                                        <div class="rating-status mt-1 small">Please click on a star to rate</div>
                                    </div>
                                    <div class="form-text">{{ form.rating.help_text }}</div>
                                </div>

                                {{ form.book_fragment|as_crispy_field }}
                                {{ form.review_text|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-success btn-lg">
                                {% if submission %}
                                    Update Submission
                                {% else %}
                                    Submit Entry
                                {% endif %}
                            </button>
                            <a href="{% url 'contest:contest_detail' slug=contest.slug %}" class="btn btn-secondary btn-lg">Cancel</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block postloadjs %}
<script>
    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM fully loaded');
    });

    $(document).ready(function() {
        // Simplified star rating using direct DOM manipulation
        console.log('Initializing star rating with direct DOM manipulation');

        // Get the rating elements
        var ratingContainer = document.querySelector('.rating-container');
        var ratingInput = document.getElementById('id_rating');
        var ratingStatus = document.querySelector('.rating-status');
        var stars = document.querySelectorAll('.rating-container i');

        console.log('Rating elements:', {
            container: ratingContainer,
            input: ratingInput,
            status: ratingStatus,
            stars: stars
        });

        if (!ratingContainer || !ratingInput || !stars.length) {
            console.log('Rating elements not found');
            return;
        }

        console.log('Rating elements found, stars count:', stars.length);

        // Set initial state
        var initialRating = parseInt(ratingInput.value) || 0;
        console.log('Initial rating:', initialRating);

        // Function to update the stars display
        function updateStars(rating) {
            console.log('Updating stars to rating:', rating);

            for (var i = 0; i < stars.length; i++) {
                var star = stars[i];
                var starRating = parseInt(star.getAttribute('data-rating'));

                if (starRating <= rating) {
                    // Selected star - solid
                    star.className = 'fas fa-star';
                    star.style.color = '#ffc107'; // Gold color
                } else {
                    // Unselected star - outline
                    star.className = 'far fa-star';
                    star.style.color = '#ccc'; // Gray color
                }
            }

            // Update the status text
            if (ratingStatus) {
                if (rating > 0) {
                    ratingStatus.textContent = 'You\'ve selected a rating of ' + rating + ' star' + (rating !== 1 ? 's' : '') + '.';
                    ratingStatus.className = 'rating-status mt-1 small text-success';
                } else {
                    ratingStatus.textContent = 'Please select a rating by clicking on the stars.';
                    ratingStatus.className = 'rating-status mt-1 small text-muted';
                }
            }
        }

        // Set initial state
        updateStars(initialRating);

        // Use a simpler approach with direct click handlers
        $('.rating-container i').on('mouseenter', function() {
            var rating = parseInt($(this).data('rating'));
            console.log('Star hover:', rating);
            updateStars(rating);
        });

        $('.rating-container i').on('click', function() {
            var rating = parseInt($(this).data('rating'));
            console.log('Star clicked:', rating);
            ratingInput.value = rating;
            updateStars(rating);

            // Add a visual confirmation
            $(this).addClass('clicked');
            setTimeout(function() {
                $('.rating-container i').removeClass('clicked');
            }, 300);
        });

        // Keyboard support
        $('.rating-container i').on('keydown', function(e) {
            // Enter or Space key
            if (e.which === 13 || e.which === 32) {
                e.preventDefault();
                $(this).click();
            }
        });

        // Container mouseleave
        $('.rating-container').on('mouseleave', function() {
            var currentRating = parseInt(ratingInput.value) || 0;
            console.log('Container mouse leave, current rating:', currentRating);
            updateStars(currentRating);
        });

        // Form validation
        var form = document.querySelector('.submission-form');
        if (form) {
            form.addEventListener('submit', function(e) {
                console.log('Form submission attempted');
                var rating = parseInt(ratingInput.value) || 0;
                var isValid = true;

                // Check if this is a second submission for a book part that already has a review
                var existingReviewMsg = document.querySelector('.alert-info.mt-3');
                var hasExistingReview = existingReviewMsg && existingReviewMsg.style.display !== 'none';

                // Skip rating validation if user already has a review for this book part
                if (rating === 0 && !hasExistingReview) {
                    e.preventDefault();
                    isValid = false;
                    alert('Please select a rating for the book part.');
                    ratingStatus.textContent = 'Rating is required. Please select a rating by clicking on the stars.';
                    ratingStatus.className = 'rating-status mt-1 small text-danger';
                    ratingContainer.scrollIntoView({ behavior: 'smooth' });
                }

                // Validate AI tools details
                var aiToolsCheckbox = document.getElementById('id_ai_tools_used');
                var aiToolsDetails = document.getElementById('id_ai_tools_details');

                if (aiToolsCheckbox && aiToolsDetails && aiToolsCheckbox.checked && !aiToolsDetails.value.trim()) {
                    e.preventDefault();
                    isValid = false;
                    alert('Please provide details about the AI tools used for your artwork.');
                    aiToolsDetails.classList.add('is-invalid');

                    // Add error message if it doesn't exist
                    var errorDiv = document.getElementById('ai-tools-details-error');
                    if (!errorDiv) {
                        errorDiv = document.createElement('div');
                        errorDiv.id = 'ai-tools-details-error';
                        errorDiv.className = 'invalid-feedback';
                        errorDiv.textContent = 'Please provide details about the AI tools used for your artwork.';
                        aiToolsDetails.parentNode.appendChild(errorDiv);
                    }

                    // Scroll to the error
                    aiToolsDetails.scrollIntoView({ behavior: 'smooth' });
                }

                return isValid;
            });
        }

        // Chapter filtering based on book part
        $(document).ready(function() {
            const bookPartSelect = document.getElementById('id_book_part');
            const chapterSelect = document.getElementById('id_chapter_reference');
            const ratingInputs = document.querySelectorAll('input[name="rating"]');
            const reviewTextArea = document.getElementById('id_review_text');
            const bookFragmentArea = document.getElementById('id_book_fragment');
            const reviewFieldsContainer = document.querySelector('.review-fields-container');
            const existingReviewMessage = document.createElement('div');
            existingReviewMessage.className = 'alert alert-info mt-3';
            existingReviewMessage.style.display = 'none';

            if (reviewFieldsContainer) {
                reviewFieldsContainer.parentNode.insertBefore(existingReviewMessage, reviewFieldsContainer.nextSibling);
            }

            // Function to check if user has already reviewed the selected book part
            function checkExistingReview(bookPartId) {
                if (!bookPartId) return;

                fetch(`/contest/api/check-review/?book_part_id=${bookPartId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.has_review) {
                            // User has already reviewed this book part
                            // Pre-populate the review fields with the existing review data
                            if (reviewTextArea) reviewTextArea.value = data.review_text;
                            if (bookFragmentArea) bookFragmentArea.value = data.book_fragment;

                            // Set the rating
                            ratingInput.value = data.rating;
                            updateStars(data.rating);

                            // Check if we're editing an existing submission or creating a new one
                            const isEditingExistingSubmission = window.location.href.includes('/edit/');
                            const isCreatingNewSubmission = window.location.href.includes('/submit/');

                            // Show message about existing review
                            existingReviewMessage.innerHTML = `
                                <strong>You've already submitted a review for this book part.</strong>
                                <p>You can enter a different book fragment and description for this submission, but only your first review will appear on the book page.</p>
                                <p>Your rating from the first submission (${data.rating} stars) will be used for all submissions for this book part.</p>
                            `;

                            // Allow editing of book fragment and review text
                            if (reviewTextArea) reviewTextArea.readOnly = false;
                            if (bookFragmentArea) bookFragmentArea.readOnly = false;

                            // But disable the star rating since it's shared across submissions
                            $('.rating-container i').css('pointer-events', 'none');
                            $('.rating-container i').css('opacity', '0.7');
                            $('.rating-status').text(`Rating from your first submission (${data.rating} stars)`);

                            // For new submissions, we'll still use the existing rating
                            // but allow users to enter their own book fragment and review text

                            existingReviewMessage.style.display = 'block';
                        } else {
                            // User has not reviewed this book part yet
                            // Clear the review fields
                            if (reviewTextArea) {
                                reviewTextArea.value = '';
                                reviewTextArea.readOnly = false;
                            }
                            if (bookFragmentArea) {
                                bookFragmentArea.value = '';
                                bookFragmentArea.readOnly = false;
                            }

                            // Clear the rating
                            ratingInput.value = 0;
                            updateStars(0);

                            // Re-enable the star rating
                            $('.rating-container i').css('pointer-events', '');
                            $('.rating-container i').css('opacity', '');
                            $('.rating-status').text('Please click on a star to rate');

                            // Hide the message
                            existingReviewMessage.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error checking for existing review:', error);
                    });
            }

            // Check for existing review when the page loads
            if (bookPartSelect && bookPartSelect.value) {
                checkExistingReview(bookPartSelect.value);
            }

            // Check for existing review when the book part changes
            if (bookPartSelect) {
                bookPartSelect.addEventListener('change', function() {
                    checkExistingReview(this.value);
                });
            }

            if (bookPartSelect && chapterSelect) {
                // Function to determine which part a book belongs to based on its book_part field or name
                function getBookPart(bookId, bookName) {
                    // First try to get the book_part attribute from the selected option
                    const selectedOption = bookPartSelect.options[bookPartSelect.selectedIndex];
                    if (selectedOption) {
                        const bookPartAttr = selectedOption.getAttribute('data-book-part');
                        if (bookPartAttr) {
                            console.log('Found book_part attribute:', bookPartAttr);
                            return bookPartAttr;
                        }
                    }

                    // Fallback to name-based detection
                    if (!bookName) return null;

                    const lowerName = bookName.toLowerCase();
                    if (lowerName.includes('part i') || lowerName.includes('part 1') || lowerName.includes('origin')) {
                        return 'part1';
                    } else if (lowerName.includes('part ii') || lowerName.includes('part 2') || lowerName.includes('scrutiny')) {
                        return 'part2';
                    } else if (lowerName.includes('part iii') || lowerName.includes('part 3') || lowerName.includes('tempus')) {
                        return 'part3';
                    }

                    // If we can't determine the part, default to showing all chapters
                    console.log('Could not determine book part, showing all chapters');
                    return null;
                }

                // Function to filter chapters based on selected book part
                function filterChapters() {
                    const selectedOption = bookPartSelect.options[bookPartSelect.selectedIndex];
                    if (!selectedOption) return;

                    const bookId = selectedOption.value;
                    const bookName = selectedOption.text;
                    const part = getBookPart(bookId, bookName);

                    console.log('Filtering chapters for book part:', bookName, 'ID:', bookId, 'detected part:', part);

                    // Store the currently selected value
                    const currentValue = chapterSelect.value;

                    // Clear all options except the placeholder
                    const placeholder = chapterSelect.querySelector('option[value=""]');
                    chapterSelect.innerHTML = '';

                    // Add back the placeholder if it exists
                    if (placeholder) {
                        chapterSelect.appendChild(placeholder);
                    } else {
                        // Create a placeholder if it doesn't exist
                        const newPlaceholder = document.createElement('option');
                        newPlaceholder.value = '';
                        newPlaceholder.textContent = 'Select a chapter';
                        chapterSelect.appendChild(newPlaceholder);
                    }

                    // Get all optgroups from the original HTML
                    const optgroups = document.querySelectorAll('#chapter-reference-template optgroup');

                    if (part) {
                        // Find the optgroup for the selected part
                        const matchingOptgroup = Array.from(optgroups).find(og => og.getAttribute('data-part') === part);

                        if (matchingOptgroup) {
                            // Clone the optgroup and add it to the select
                            const clonedOptgroup = matchingOptgroup.cloneNode(true);
                            chapterSelect.appendChild(clonedOptgroup);

                            // Try to restore the previously selected value if it's in this optgroup
                            if (currentValue) {
                                const matchingOption = clonedOptgroup.querySelector(`option[value="${currentValue}"]`);
                                if (matchingOption) {
                                    matchingOption.selected = true;
                                } else {
                                    // Select the first option in the optgroup if the previous value isn't available
                                    const firstOption = clonedOptgroup.querySelector('option');
                                    if (firstOption) {
                                        firstOption.selected = true;
                                    }
                                }
                            }
                        }
                    } else {
                        // If no part is selected, add all optgroups
                        optgroups.forEach(og => {
                            const clonedOptgroup = og.cloneNode(true);
                            chapterSelect.appendChild(clonedOptgroup);
                        });

                        // Try to restore the previously selected value
                        if (currentValue) {
                            const matchingOption = chapterSelect.querySelector(`option[value="${currentValue}"]`);
                            if (matchingOption) {
                                matchingOption.selected = true;
                            }
                        }
                    }
                }

                // Filter chapters on page load
                $(document).ready(function() {
                    console.log('Document ready, filtering chapters on page load');

                    // Log all book part options and their data-book-part attributes
                    console.log('Book part options:');
                    for (let i = 0; i < bookPartSelect.options.length; i++) {
                        const option = bookPartSelect.options[i];
                        console.log(`Book part option ${i}:`, {
                            value: option.value,
                            text: option.text,
                            'data-book-part': option.getAttribute('data-book-part')
                        });
                    }

                    // Log all chapter template optgroups
                    console.log('Chapter template optgroups:');
                    const optgroups = document.querySelectorAll('#chapter-reference-template optgroup');
                    optgroups.forEach((og, index) => {
                        console.log(`Optgroup ${index}:`, {
                            label: og.getAttribute('label'),
                            'data-part': og.getAttribute('data-part')
                        });
                    });

                    filterChapters();
                });

                // Filter chapters when book part changes
                $(bookPartSelect).on('change', function() {
                    console.log('Book part changed to:', this.value);
                    const selectedOption = bookPartSelect.options[bookPartSelect.selectedIndex];
                    console.log('Selected option:', {
                        value: selectedOption.value,
                        text: selectedOption.text,
                        'data-book-part': selectedOption.getAttribute('data-book-part')
                    });
                    filterChapters();
                });
            }
        });

        // 3D Model URL Toggle based on category
        console.log('Setting up 3D model URL toggle');
        const $categorySelect = $('#id_category');
        const $model3dUrlContainer = $('#model-3d-url-container');

        if ($categorySelect.length && $model3dUrlContainer.length) {
            console.log('3D model URL elements found');

            // Function to check if the selected category is a 3D category
            function checkFor3DCategory() {
                const selectedCategory = $categorySelect.find('option:selected').text();
                console.log('Selected category:', selectedCategory);

                if (selectedCategory.includes('3D')) {
                    console.log('3D category detected, showing 3D model URL field');
                    $model3dUrlContainer.removeClass('d-none');
                } else {
                    console.log('Non-3D category detected, hiding 3D model URL field');
                    $model3dUrlContainer.addClass('d-none');
                    // Clear the field when hidden
                    $('#id_model_3d_url').val('');
                }
            }

            // Check on page load
            checkFor3DCategory();

            // Check when category changes
            $categorySelect.on('change', function() {
                checkFor3DCategory();
            });
        } else {
            console.error('3D model URL elements not found:', {
                category: $categorySelect.length,
                container: $model3dUrlContainer.length
            });
        }

        // AI Tools Details Toggle
        console.log('Setting up AI tools toggle');

        // Use jQuery for more reliable element selection
        const $aiToolsCheckbox = $('#id_ai_tools_used');
        const $aiToolsDetails = $('#ai-tools-details');

        if ($aiToolsCheckbox.length && $aiToolsDetails.length) {
            console.log('AI tools elements found');

            // Set initial state based on checkbox
            if ($aiToolsCheckbox.is(':checked')) {
                $aiToolsDetails.removeClass('d-none');
            } else {
                $aiToolsDetails.addClass('d-none');
            }

            // Add change event listener
            $aiToolsCheckbox.on('change', function() {
                console.log('AI tools checkbox changed:', this.checked);
                if (this.checked) {
                    $aiToolsDetails.removeClass('d-none');
                    // Make the details field required when AI tools are used
                    $('#id_ai_tools_details').prop('required', true);
                } else {
                    $aiToolsDetails.addClass('d-none');
                    // Make the details field not required when AI tools are not used
                    $('#id_ai_tools_details').prop('required', false);
                }
            });

            // Set initial required state
            $('#id_ai_tools_details').prop('required', $aiToolsCheckbox.is(':checked'));
        } else {
            console.error('AI tools elements not found:', {
                checkbox: $aiToolsCheckbox.length,
                details: $aiToolsDetails.length
            });
        }
    });
</script>
{% endblock %}