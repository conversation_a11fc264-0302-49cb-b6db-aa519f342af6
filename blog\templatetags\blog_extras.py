from django import template

register = template.Library()


@register.filter
def get_featured_image_url(post, crop_type='featured'):
    """
    Template filter to get cropped featured image URL.
    Usage: {{ post|get_featured_image_url:'card' }}
    """
    if hasattr(post, 'get_featured_image_url'):
        return post.get_featured_image_url(crop_type)
    return post.post_featured_image.url if post.post_featured_image else None
