from django import forms
from .models import Comment, Post, PostImage
from utils.image_validation import validate_image
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class CommentForm(forms.ModelForm):
    """
    Form for creating and editing blog post comments.
    """

    class Meta:
        """
        Meta class for CommentForm.
        """
        model = Comment
        fields = ('content',)
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Write your comment here...'
            })
        }


class PostForm(forms.ModelForm):
    """
    Form for creating and editing blog posts.
    Includes image validation.
    """
    post_featured_image = forms.ImageField(
        validators=[validate_image],
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/jpeg,image/png,image/webp'
        })
    )

    class Meta:
        model = Post
        fields = ['post_title', 'post_content', 'post_featured_image', 'post_status', 'post_excerpt', 'category', 'tags']
        widgets = {
            'post_content': forms.Textarea(attrs={'class': 'form-control'}),
            'post_excerpt': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3
            })
        }


class PostImageForm(forms.ModelForm):
    """
    Form for blog post content images.
    Includes image validation and standardization.
    """
    image = forms.ImageField(
        validators=[validate_image],
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/jpeg,image/png,image/webp'
        })
    )
    
    class Meta:
        model = PostImage
        fields = ['image', 'alt_text', 'caption', 'width', 'alignment']
        widgets = {
            'alt_text': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Required: Describe the image for screen readers'
            }),
            'caption': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Optional: Caption to display below the image'
            }),
            'width': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '200',
                'max': '1200'
            })
        }

    def clean_image(self):
        """
        Validate and standardize uploaded images.
        """
        image = self.cleaned_data.get('image')
        if image:
            # Check file size
            if image.size > settings.MAX_UPLOAD_SIZE:
                raise forms.ValidationError(
                    f'Image too large. Max size is {settings.MAX_UPLOAD_SIZE/1024/1024}MB.'
                )
                
            # Log information about the image
            logger.info(f'Processing image upload: {image.name}, {image.size} bytes')
                
        return image
